apiVersion: extensions.istio.io/v1alpha1
kind: WasmPlugin
metadata:
  name: request-path-episode
spec:
  selector:
    matchLabels:
      app: episode
  url: https://storage.googleapis.com/istio-build/proxy/attributegen-359dcd3a19f109c50e97517fe6b1e2676e870c4d.wasm
  imagePullPolicy: IfNotPresent
  phase: AUTHN
  pluginConfig:
    attributes:
      - output_attribute: path
        match:
          - value: /metrics
            condition: request.url_path.matches('^/metrics$')
          - value: /episode/info
            condition: request.url_path.matches('^/episode/info$')
          - value: /api/admins/episode/all
            condition: request.url_path.matches('^/api/admins/episode/all$')
          - value: /api/admins/episode/migrate
            condition: request.url_path.matches('^/api/admins/episode/migrate$')
          - value: /api/users/{userId}/episode/create
            condition: request.url_path.matches('^/api/users/(.*?)/episode/create$')
          - value: /api/users/{userId}/episode/update
            condition: request.url_path.matches('^/api/users/(.*?)/episode/update')
          - value: /api/public/episode/{episodeId}
            condition: request.url_path.matches('^/api/public/episode/(.*?)$')
          - value: /api/public/episode/search
            condition: request.url_path.matches('^/api/public/episode/search')
          - value: /api/public/episode/feed
            condition: request.url_path.matches('^/api/public/episode/feed$')
          - value: /api/users/{userId}/episode/ids
            condition: request.url_path.matches('^/api/users/(.*?)/episode/ids$')
          - value: /api/users/{userId}/episode/copy/{episodeId}
            condition: request.url_path.matches('^/api/users/(.*?)/episode/copy/(.*?)$')
          - value: /api/users/{userId}/episode/find
            condition: request.url_path.matches('^/api/users/(.*?)/episode/find$')
          - value: /api/users/{userId}/episode/search
            condition: request.url_path.matches('^/api/users/(.*?)/episode/search$')
          - value: /api/users/{userId}/episode/feed
            condition: request.url_path.matches('^/api/users/(.*?)/episode/feed')
          - value: /api/users/{userId}/episode/{episodeId}/details
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/details$')
          - value: /api/users/{userId}/episode/{episodeId}/metrics
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/metrics$')
          - value: /api/users/{userId}/episode/{episodeId}/analytics
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/analytics$')
          - value: /api/users/{userId}/episode/{episodeId}/play
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/play$')
          - value: /api/users/{userId}/episode/{episodeId}/finish
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/finish$')
          - value: /api/users/{userId}/episode/{episodeId}/friends
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/friends$')
          - value: /api/users/{userId}/episode/recommendation
            condition: request.url_path.matches('^/api/users/(.*?)/episode/recommendation$')
          - value: /api/users/{userId}/episode/overview
            condition: request.url_path.matches('^/api/users/(.*?)/episode/overview$')
          - value: /api/users/{userId}/episode/overview/channels
            condition: request.url_path.matches('^/api/users/(.*?)/episode/overview/channels$')
          - value: /api/users/{userId}/episode/accounts
            condition: request.url_path.matches('^/api/users/(.*?)/episode/accounts$')
          - value: /api/users/{userId}/episode/{episodeId}/like
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/like$')
          - value: /api/users/{userId}/episode/{episodeId}/dislike
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/dislike$')
          - value: /api/users/{userId}/episode/{episodeId}/progress/{contentId}
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/progress/(.*?)$')
          - value: /api/users/{userId}/episode/{episodeId}/answer/{contentId}
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/answer/(.*?)$')
          - value: /api/users/{userId}/episode/{episodeId}/report
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/report$')
          - value: /api/users/{userId}/episode/{episodeId}/reports
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)/reports$')
          - value: /api/users/{userId}/episode/{episodeId}
            condition: request.url_path.matches('^/api/users/(.*?)/episode/(.*?)$')
          - value: /api/users/{userId}/reports
            condition: request.url_path.matches('^/api/users/(.*?)/reports$')
          - value: /api/admins/episode/{episodeId}
            condition: request.url_path.matches('^/api/admins/episode/(.*?)$')
          - value: /api/admins/episode/register/quality
            condition: request.url_path.matches('^/api/admins/episode/register/quality$')
          - value: /api/users/{userId}/channel-episodes/create
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/create$')
          - value: /api/users/{userId}/channel-episodes/search
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/search$')
          - value: /api/users/{userId}/channel-episodes/search/by-language/{language}
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/search/by-language/(.*?)$')
          - value: /api/users/{userId}/channel-episodes/{channelId}
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/(.*?)$')
          - value: /api/users/{userId}/channel-episodes/{channelId}/reduced
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/(.*?)/reduced$')
          - value: /api/users/{userId}/channel-episodes/{channelId}/order-type
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/(.*?)/order-type$')
          - value: /api/users/{userId}/channel-episodes/{channelId}/episodes/add
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/(.*?)/episodes/add$')
          - value: /api/users/{userId}/channel-episodes/{channelId}/episodes/remove
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/(.*?)/episodes/remove$')
          - value: /api/users/{userId}/channel-episodes/{channelId}/episodes/order
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/(.*?)/episodes/order$')
          - value: /api/users/{userId}/channel-episodes/profile/{profileOwnerId}/summary
            condition: request.url_path.matches('^/api/users/(.*?)/channel-episodes/profile/(.*?)/summary$')
          - value: /api/users/{userId}/challenge-episodes/create
            condition: request.url_path.matches('^/api/users/(.*?)/challenge-episodes/create$')
          - value: /api/users/{userId}/challenge-episodes/{challengeId}
            condition: request.url_path.matches('^/api/users/(.*?)/challenge-episodes/(.*?)$')
