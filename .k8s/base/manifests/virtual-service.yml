apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: episode-vs
spec:
  hosts:
  gateways:
    - api-host-gwy
  http:
    - match:
        - uri:
            regex: /api/admins/episode/.*
        - uri:
            regex: /api/users/.*/episode/.*
        - uri:
            regex: /api/episode/.*
        - uri:
            regex: /api/admins/channel-episodes/.*
        - uri:
            regex: /api/admins/.*/episodes/.*/reports
        - uri:
            regex: /api/users/.*/channel-episodes/.*
        - uri:
            regex: /api/channel-episodes/.*
        - uri:
            regex: /api/users/.*/challenge-episodes/.*
      route:
        - destination:
            host: episode
    - match:
        - uri:
            regex: /api/public/episode/.*
      route:
        - destination:
            host: episode
          headers:
            request:
              set:
                X-Gateway-Traffic: "Overridden"
      corsPolicy:
        allowOrigin:
          - "*"
        allowMethods:
          - GET
          - OPTIONS
        allowHeaders:
          - "*"
        allowCredentials: true