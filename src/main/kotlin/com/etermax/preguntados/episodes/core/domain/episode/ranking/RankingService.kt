package com.etermax.preguntados.episodes.core.domain.episode.ranking

import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService

class RankingService(
    private val rankingRepository: RankingRepository,
    private val deliveryRankingService: DeliveryRankingService,
    private val playerFriendsService: PlayerFriendsService
) {

    suspend fun findRanking(playerId: Long, domain: Domain, rankingSize: Int = RANKING_SIZE): DeliveryRanking {
        return rankingRepository.find(playerId, domain, rankingSize)?.let {
            deliveryRankingService.complete(it)
        } ?: DeliveryRanking.EMPTY
    }

    suspend fun findRankingWithFriends(playerId: Long, domain: Domain): DeliveryRanking {
        val followedPlayers = playerFriendsService.findFollowedIds(playerId)
        val rankingPositions = rankingRepository.findForPlayers(playerId, domain, followedPlayers.plus(playerId))
        val ranking = Ranking(
            players = rankingPositions,
            player = rankingPositions.firstOrNull { it.playerId == playerId }
                ?: RankedPlayer(playerId, RankingEntry(position = rankingPositions.size + 1, points = 0))
        )
        return deliveryRankingService.complete(ranking)
    }

    suspend fun incrementScore(playerId: Long, domain: Domain, score: Int) {
        rankingRepository.incrementScore(playerId, domain, score)
    }

    private companion object {
        const val RANKING_SIZE = 100
    }
}
