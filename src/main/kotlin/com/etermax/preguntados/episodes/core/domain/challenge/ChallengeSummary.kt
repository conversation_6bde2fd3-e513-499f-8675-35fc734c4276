package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import java.time.OffsetDateTime

data class ChallengeSummary(
    val id: String,
    val startDate: OffsetDateTime,
    val endDate: OffsetDateTime,
    val ownerId: Long,
    val episode: EpisodeSummary,
    val ranking: DeliveryRanking,
    val players: ChallengePlayersSummary
) {
    companion object {
        fun from(challengeDetails: ChallengeDetails) = with(challengeDetails) {
            ChallengeSummary(
                id = challenge.id,
                startDate = challenge.startDate,
                endDate = challenge.endDate,
                ownerId = challenge.ownerId,
                episode = EpisodeSummary.from(episode, profileService.find(episode.ownerId)),
                ranking = ranking,
                players = ChallengePlayersSummary(players.map { ChallengePlayerSummary.from(it, profileService.find(it.playerId)) })
            )
        }

        fun from(
            challenge: Challenge,
            ranking: DeliveryRanking,
            players: ChallengePlayersSummary,
            episode: EpisodeSummary
        ) =
            with(challenge) {
                val original = EpisodeSummary(
                    id = episode.id,
                    name = challenge.reference.name,
                    language = episode.language,
                    country = episode.country,
                    type = episode.type,
                    startDate = episode.startDate,
                    cover = challenge.reference.cover,
                    banner = episode.banner,
                    ownerId = episode.ownerId,
                    owner = episode.owner,
                    contents = challenge.reference.contents,
                    views = episode.views,
                    rate = episode.rate,
                    status = episode.status,
                    channelId = episode.channelId
                )

                ChallengeSummary(
                    id = id,
                    startDate = startDate,
                    endDate = endDate,
                    ownerId = ownerId,
                    episode = original,
                    ranking = ranking,
                    players = players
                )
            }
    }
}
