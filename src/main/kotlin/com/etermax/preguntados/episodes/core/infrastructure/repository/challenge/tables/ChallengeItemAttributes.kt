package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables

object ChallengeItemAttributes {
    const val PK = "PK"
    const val SK = "SK"
    const val CHALLENGE_ID = "challenge_id"
    const val CREATE_DATE = "create_date"
    const val START_DATE = "start_date"
    const val END_DATE = "end_date"
    const val EXPIRE_DATE = "expiration"
    const val OWNER_ID = "owner_id"
    const val REF_EPISODE_ID = "ref_episode_id"
    const val REF_NAME = "ref_name"
    const val REF_COVER = "ref_cover"
    const val REF_CONTENTS = "ref_contents"
}

object ChallengePlayerItemAttributes {
    const val PK = "PK"
    const val SK = "SK"
    const val PLAYER_ID = "player_id"
    const val CHALLENGE_ID = "challenge_id"
    const val STATUS = "status"
}
