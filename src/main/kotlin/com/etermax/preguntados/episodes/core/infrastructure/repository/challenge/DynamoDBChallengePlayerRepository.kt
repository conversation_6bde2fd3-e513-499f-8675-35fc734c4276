package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengePlayerItem
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapMerge
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest

class DynamoDBChallengePlayerRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<ChallengePlayerItem>,
    val byPlayerIndex: DynamoDbAsyncIndex<ChallengePlayerItem>
) : ChallengePlayerRepository, DynamoDBRepository<ChallengePlayerItem>(client, table) {
    override suspend fun save(challengePlayers: List<ChallengePlayer>) {
        challengePlayers.chunked(25).forEach { chunksChallengePlayers ->
            val challengePlayersBatch = chunksChallengePlayers.map {
                makeWriteBatch(ChallengePlayerItem.from(it), ChallengePlayerItem::class.java)
            }
            addItemsBulkWithRetry(challengePlayersBatch, ChallengePlayerItem::class.java)
        }
    }

    override suspend fun save(challengePlayer: ChallengePlayer) {
        save(listOf(challengePlayer))
    }

    override suspend fun findBy(
        playerId: Long,
        challengeId: String
    ): ChallengePlayer? {
        return findItem(
            Key.builder()
                .partitionValue(ChallengePlayerItem.buildPartitionKey(challengeId))
                .sortValue(ChallengePlayerItem.buildSortedKey(playerId))
                .build()
        )?.toDomain()
    }

    @OptIn(FlowPreview::class)
    override suspend fun findAllByChallengeId(challengeId: String): List<ChallengePlayer> {
        val partitionKey = ChallengePlayerItem.buildPartitionKey(challengeId)
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(partitionKey)
        }

        val request = QueryEnhancedRequest.builder()
            .queryConditional(queryConditional)
            .build()

        return table
            .query(request)
            .asFlow()
            .flatMapMerge { page -> page.items().asFlow() }
            .mapNotNull { it.toDomain() }.toList()
    }

    @OptIn(FlowPreview::class)
    override suspend fun findAllByPlayerId(playerId: Long): List<ChallengePlayer> {
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(playerId)
        }

        val request = QueryEnhancedRequest.builder()
            .queryConditional(queryConditional)
            .build()

        return byPlayerIndex
            .query(request)
            .asFlow()
            .flatMapMerge { page -> page.items().asFlow() }
            .mapNotNull { it.toDomain() }.toList()
    }
}
