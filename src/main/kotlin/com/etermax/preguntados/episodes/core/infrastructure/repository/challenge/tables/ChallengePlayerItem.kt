package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.Status
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*

@DynamoDbBean
class ChallengePlayerItem(
    @get:DynamoDbAttribute(ChallengePlayerItemAttributes.PLAYER_ID)
    @get:DynamoDbSecondaryPartitionKey(indexNames = [ChallengeIndexes.BY_PLAYER])
    var playerId: Long = 0,
    @get:DynamoDbAttribute(ChallengePlayerItemAttributes.CHALLENGE_ID)
    @get:DynamoDbSecondarySortKey(indexNames = [ChallengeIndexes.BY_PLAYER])
    var challengeId: String = "",
    @get:DynamoDbAttribute(ChallengePlayerItemAttributes.STATUS)
    var status: String = Status.PENDING.name
) {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(ChallengePlayerItemAttributes.PK)
    var pk: String = buildPartitionKey(challengeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(ChallengePlayerItemAttributes.SK)
    var sk: String = buildSortedKey(playerId)

    fun toDomain(): ChallengePlayer = ChallengePlayer(
        playerId = playerId,
        challengeId = challengeId,
        status = Status.valueOf(status)
    )

    companion object {
        const val CHALLENGE_PLAYER_PREFIX = "CH_PLAYER#"

        fun buildPartitionKey(challengeId: String) = "$CHALLENGE_PLAYER_PREFIX$challengeId"

        fun buildSortedKey(playerId: Long) = "$CHALLENGE_PLAYER_PREFIX$playerId"

        fun from(player: ChallengePlayer) = with(player) {
            ChallengePlayerItem(
                playerId = playerId,
                challengeId = challengeId,
                status = status.name
            )
        }
    }
}
