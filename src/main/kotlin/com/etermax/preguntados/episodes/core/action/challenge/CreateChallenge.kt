package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.*
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer

class CreateChallenge(
    private val challengesRepository: ChallengeRepository,
    private val challengePlayersRepository: ChallengePlayerRepository,
    private val episodesRepository: EpisodeRepository,
    private val uuidSequencer: UUIDSequencer,
    private val summaryService: SummaryService,
    val clock: Clock
) {
    suspend operator fun invoke(actionData: ActionData): ChallengeSummary {
        return with(actionData) {
            val episode = episodesRepository.findById(episodeId) ?: throw EpisodeNotFoundException(episodeId)

            val now = clock.now()
            val challenge = Challenge(
                id = uuidSequencer.next(),
                createDate = now,
                startDate = now,
                endDate = now.plusSeconds(durationInSeconds),
                expireDate = now.plusSeconds(durationInSeconds).plusDays(1), // TODO Define expiration interval
                ownerId = playerId,
                reference = EpisodeReference(episode.id, episode.name, episode.cover, episode.contents)
            )

            challengesRepository.save(challenge)
            val owner = addOwnerToChallenge(challenge)

            ChallengeSummary(
                id = challenge.id,
                episode = summaryService.toEpisodeSummary(episode)!!,
                startDate = challenge.startDate,
                endDate = challenge.endDate,
                ownerId = challenge.ownerId,
                ranking = DeliveryRanking.EMPTY,
                players = summaryService.toChallengePlayersSummary(listOf(owner))
            )
        }
    }

    private suspend fun ActionData.addOwnerToChallenge(
        challenge: Challenge
    ): ChallengePlayer {
        val owner = ChallengePlayer(
            playerId = playerId,
            challengeId = challenge.id,
            status = Status.PLAYING
        )

        challengePlayersRepository.save(owner)
        return owner
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val durationInSeconds: Long
    )
}
