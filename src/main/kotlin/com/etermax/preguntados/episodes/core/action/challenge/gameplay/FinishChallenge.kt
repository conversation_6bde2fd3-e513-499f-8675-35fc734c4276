package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.finish.FinishEpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class FinishChallenge(
    private val challengeRepository: ChallengeRepository,
    private val episodesRepository: EpisodeRepository,
    private val profileService: ProfileService,
    private val rankingService: RankingService
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): FinishEpisodeDetails = coroutineScope {
        with(actionData) {
            logger.info("Player $playerId finished challenge $challengeId")

            val challenge = challengeRepository.find(challengeId) ?: throw ChallengeNotFoundException(challengeId)
            val episode = episodesRepository.findById(challenge.reference.episodeId) ?: throw EpisodeNotFoundException(
                challenge.reference.episodeId
            )

            val scope = "${episode.id}_$challengeId"
            val domain = Domain(scope)

            val ranking = async { rankingService.findRanking(playerId, domain) }

            val episodeSummary = async { getSummary(episode) }


            // TODO summary del challenge
            // TODO sugerencia de nuevos challenge

            FinishEpisodeDetails(
                episodeSummary = episodeSummary.await(),
                rate = null,
                ranking = ranking.await(),
                rankingWithFriends = null
            )
        }
    }

    private suspend fun getSummary(episode: Episode): EpisodeSummary {
        val profile = profileService.find(episode.ownerId)
        return EpisodeSummary.from(episode, profile)
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String
    )
}
