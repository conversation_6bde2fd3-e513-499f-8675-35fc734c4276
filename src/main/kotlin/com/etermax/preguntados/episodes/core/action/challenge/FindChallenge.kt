package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class FindChallenge(
    val challengesRepository: ChallengeRepository,
    val challengePlayerRepository: ChallengePlayerRepository,
    val episodesRepository: EpisodeRepository,
    val rankingService: RankingService,
    val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData): ChallengeSummary? = coroutineScope {
        with(actionData) {
            val challenge = challengesRepository.find(challengeId) ?: throw ChallengeNotFoundException(challengeId)
            val episode = episodesRepository.findById(challenge.reference.episodeId) ?: throw EpisodeNotFoundException(
                challenge.reference.episodeId
            )

            val scope = "${episode.id}_$challengeId"
            val domain = Domain(scope)
            val playersStatusTask = async { challengePlayerRepository.findAllByChallengeId(challengeId) }
            val playersRankingTask = async { rankingService.findRanking(playerId, domain) }

            val playersStatus = playersStatusTask.await()
            val playersRanking = playersRankingTask.await()

            ChallengeSummary.from(
                challenge,
                playersRanking,
                summaryService.toChallengePlayersSummary(playersStatus),
                summaryService.toEpisodeSummary(episode)!!
            )
        }
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String
    )
}
