package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository

class FindChallenge(
    val challengesRepository: ChallengeRepository,
    val challengePlayerRepository: ChallengePlayerRepository,
    val episodesRepository: EpisodeRepository,
    val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData): ChallengeSummary? = with(actionData) {
        val challenge = challengesRepository.find(challengeId) ?: return null
        val episode = episodesRepository.findById(challenge.reference.episodeId) ?: return null
        val players = challengePlayerRepository.findAll(challengeId)
        ChallengeSummary.from(
            challenge,
            summaryService.toChallengePlayersSummary(players),
            // TODO Get Ranking
            summaryService.toEpisodeSummary(episode)!!
        )
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String
    )
}
