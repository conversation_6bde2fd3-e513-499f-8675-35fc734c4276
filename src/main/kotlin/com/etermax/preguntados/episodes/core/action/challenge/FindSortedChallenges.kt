package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeDetails
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.Status
import com.etermax.preguntados.sortable.games.core.domain.pagination.EpochMills
import com.etermax.preguntados.sortable.games.core.domain.pagination.PlayerId
import com.etermax.preguntados.sortable.games.core.domain.pagination.SortableGame
import com.etermax.preguntados.sortable.games.core.domain.pagination.SortedGames
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

class FindSortedChallenges(
    val sortedGames: SortedGames,
    val challengeService: ChallengeService
) {
    suspend operator fun invoke(actionData: ActionData) =
        coroutineScope {
            with(actionData) {
                val challenges = challengeService.getChallengeDetailsByUser(playerId)
                val challengesById = challenges.associateBy { c -> c.challenge.id }
                updateSortedGames(challenges)

                val (games, pendingGames) = awaitAll(
                    async { getGames(challengesById) },
                    async { getPendingGames(challengesById) }
                )

                SortedChallenges(
                    challenges = games,
                    pendingChallenges = pendingGames
                )
            }
        }

    suspend fun updateSortedGames(challenges: List<ChallengeDetails>) {
        challenges.forEach { it ->
            sortedGames.add(it.toSortableGame())
        }
    }

    private fun ChallengeDetails.toSortableGame(): SortableGame {
        return ChallengeSortableGame(this)
    }

    private suspend fun ActionData.getGames(challengesById: Map<String, ChallengeDetails>): List<ChallengeDetails> {
        return gamesPagination?.let {
            sortedGames.getGamesFor(playerId, it.amount, it.skip).mapNotNull { challengesById[it.id] }
        } ?: emptyList()
    }

    private suspend fun ActionData.getPendingGames(challengesById: Map<String, ChallengeDetails>): List<ChallengeDetails> {
        return pendingGamesPagination?.let {
            sortedGames.getPendingGames(playerId, it.amount, it.skip).mapNotNull { challengesById[it.id] }
        } ?: emptyList()
    }

    data class SortedChallenges(
        val challenges: List<ChallengeDetails>,
        val pendingChallenges: List<ChallengeDetails>
    )

    data class ActionData(
        val playerId: Long,
        val gamesPagination: Pagination? = null,
        val pendingGamesPagination: Pagination? = null
    ) {
        data class Pagination(
            val amount: Int,
            val skip: Int
        )
    }

    class ChallengeSortableGame(private val details: ChallengeDetails) : SortableGame {
        override val id = details.challenge.id

        override suspend fun hasPlayerWon(playerId: PlayerId): Boolean {
            return isFinished() && details.getPlayerPosition() == 1
        }

        override fun isFinished(): Boolean {
            return details.isFinished()
        }

        override fun isPlayerAwaitingToJoin(playerId: Long): Boolean {
            return details.getPlayerStatus(playerId) == Status.PENDING
        }

        override fun isPlayerTurn(playerId: PlayerId): Boolean {
            return details.getPlayerStatus(playerId) == Status.PLAYING
        }

        override fun playersIsAvailableFor(): List<PlayerId> {
            return details.players.map { it.playerId }.toList()
        }

        override suspend fun sortingTime(playerId: Long): EpochMills {
            return details.challenge.endDate.toInstant().toEpochMilli()
        }
    }
}
