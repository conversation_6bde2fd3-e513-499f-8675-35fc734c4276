package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayersSummary
import com.etermax.preguntados.episodes.core.domain.challenge.Status

class ChallengePlayers(
    private val challengePlayerRepository: ChallengePlayerRepository,
    private val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData): ChallengePlayersSummary {
        return with(actionData) {
            if (userIds.isEmpty()) {
                return ChallengePlayersSummary(
                    players = emptyList()
                )
            }

            val players = userIds.map {
                ChallengePlayer(
                    challengeId = challengeId,
                    playerId = it,
                    status = Status.PENDING
                )
            }

            challengePlayerRepository.save(players)
            val allPlayers = challengePlayerRepository.findAllByChallengeId(challengeId)
            summaryService.toChallengePlayersSummary(allPlayers)
        }
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String,
        val userIds: Set<Long>
    )
}
