package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables

import com.etermax.preguntados.episodes.core.domain.challenge.Challenge
import com.etermax.preguntados.episodes.core.domain.challenge.EpisodeReference
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@DynamoDbBean
class ChallengeItem(
    @get:DynamoDbAttribute(ChallengeItemAttributes.CHALLENGE_ID)
    var challengeId: String = "",
    @get:DynamoDbAttribute(ChallengeItemAttributes.CREATE_DATE)
    @get:DynamoDbSecondarySortKey(indexNames = [ChallengeIndexes.BY_OWNER_AND_CREATION_DATE])
    var createDate: Long = 0,
    @get:DynamoDbAttribute(ChallengeItemAttributes.START_DATE)
    var startDate: Long = 0,
    @get:DynamoDbAttribute(ChallengeItemAttributes.END_DATE)
    var endDate: Long = 0,
    @get:DynamoDbAttribute(ChallengeItemAttributes.EXPIRE_DATE)
    var expireDate: Long = 0,
    @get:DynamoDbSecondaryPartitionKey(indexNames = [ChallengeIndexes.BY_OWNER_AND_CREATION_DATE])
    @get:DynamoDbAttribute(ChallengeItemAttributes.OWNER_ID)
    var ownerId: Long = 0,
    @get:DynamoDbAttribute(ChallengeItemAttributes.REF_EPISODE_ID)
    var episodeId: String = "",
    @get:DynamoDbAttribute(ChallengeItemAttributes.REF_NAME)
    var name: String = "",
    @get:DynamoDbAttribute(ChallengeItemAttributes.REF_COVER)
    var cover: String = "",
    @get:DynamoDbAttribute(ChallengeItemAttributes.REF_CONTENTS)
    var contents: List<String> = emptyList()
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(ChallengeItemAttributes.PK)
    var pk: String = buildPartitionKey(challengeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(ChallengeItemAttributes.SK)
    var sk: String = CHALLENGE_SK

    fun toDomain(): Challenge = Challenge(
        id = challengeId,
        createDate = OffsetDateTime.ofInstant(Instant.ofEpochMilli(createDate), ZoneOffset.UTC),
        startDate = OffsetDateTime.ofInstant(Instant.ofEpochMilli(startDate), ZoneOffset.UTC),
        endDate = OffsetDateTime.ofInstant(Instant.ofEpochMilli(endDate), ZoneOffset.UTC),
        expireDate = OffsetDateTime.ofInstant(Instant.ofEpochMilli(expireDate), ZoneOffset.UTC),
        ownerId = ownerId,
        reference = EpisodeReference(episodeId, name, cover, contents)
    )

    companion object {
        const val CHALLENGE_PREFIX = "CH#"
        const val CHALLENGE_SK = "CHALLENGE"

        fun buildPartitionKey(challengeId: String) = "$CHALLENGE_PREFIX$challengeId"

        fun from(challenge: Challenge) = with(challenge) {
            ChallengeItem(
                challengeId = id,
                createDate = createDate.toMillis(),
                startDate = startDate.toMillis(),
                expireDate = expireDate.toMillis(),
                endDate = endDate.toMillis(),
                ownerId = ownerId,
                episodeId = reference.episodeId,
                name = reference.name,
                cover = reference.cover,
                contents = reference.contents
            )
        }
    }
}
