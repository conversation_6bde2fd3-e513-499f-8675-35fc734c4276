package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges.ActionData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GetSortedGamesRepresentation(
    @SerialName("games_pagination") val gamesPagination: PaginationRepresentation? = null,
    @SerialName("pending_pagination") val pendingGamesPagination: PaginationRepresentation? = null
) {
    fun to(playerId: Long) = ActionData(
        playerId = playerId,
        gamesPagination = gamesPagination?.to(),
        pendingGamesPagination = pendingGamesPagination?.to()
    )
}

@Serializable
data class PaginationRepresentation(
    @SerialName("amount") val amount: Int,
    @SerialName("skip") val skip: Int
) {
    fun to() = ActionData.Pagination(amount, skip)
}
