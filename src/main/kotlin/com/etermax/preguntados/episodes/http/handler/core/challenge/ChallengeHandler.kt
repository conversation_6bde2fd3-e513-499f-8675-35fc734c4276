package com.etermax.preguntados.episodes.http.handler.core.challenge

import com.etermax.preguntados.episodes.core.action.challenge.ChallengePlayers
import com.etermax.preguntados.episodes.core.action.challenge.CreateChallenge
import com.etermax.preguntados.episodes.core.action.challenge.FindChallenge
import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.AnswerChallengeContent
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.FinishChallenge
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.PlayChallenge
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.RegisterChallengeProgress
import com.etermax.preguntados.episodes.http.challengeId
import com.etermax.preguntados.episodes.http.getFromPath
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.challenge.representation.ChallengePlayersRepresentation
import com.etermax.preguntados.episodes.http.handler.core.challenge.representation.ChallengePlayersSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.challenge.representation.ChallengeSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.challenge.representation.CreateChallengeRepresentation
import com.etermax.preguntados.episodes.http.handler.core.challenge.representation.GetSortedGamesRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.*
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class ChallengeHandler(
    val createChallenge: CreateChallenge,
    val findChallenge: FindChallenge,
    val findSortedChallenges: FindSortedChallenges,
    val challengePlayers: ChallengePlayers,
    val playChallenge: PlayChallenge,
    val answerChallenge: AnswerChallengeContent,
    val progressChallenge: RegisterChallengeProgress,
    val finishChallenge: FinishChallenge
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/challenge-episodes") {
                route("/create") {
                    post { createHandler() }
                }
                route("/sorted-games") {
                    get { getSortedGamesHandler() }
                }
                route("/{challengeId}") {
                    post { challengeFriendsHandler() }

                    get { getHandler() }

                    route("/play") {
                        post { playHandler() }
                    }
                    route("/finish") {
                        get { finishHandler() }
                    }
                    route("/progress/{contentId}") {
                        post { registerProgressHandler() }
                    }
                    route("/answer/{contentId}") {
                        post { answerHandler() }
                    }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.createHandler() {
        val actionData = call.receive<CreateChallengeRepresentation>().to(userId)
        val summary = createChallenge(actionData)
        call.respond(HttpStatusCode.Companion.Created, ChallengeSummaryRepresentation.from(summary))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getSortedGamesHandler() {
        val actionData = call.receive<GetSortedGamesRepresentation>().to(userId)
        findSortedChallenges(actionData)
        call.respond(HttpStatusCode.OK)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.challengeFriendsHandler() {
        val actionData = call.receive<ChallengePlayersRepresentation>().to(userId, challengeId)
        val summary = challengePlayers(actionData)
        call.respond(HttpStatusCode.OK, ChallengePlayersSummaryRepresentation.from(summary))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getHandler() {
        findChallenge(FindChallenge.ActionData(userId, challengeId))?.let {
            call.respond(HttpStatusCode.OK, ChallengeSummaryRepresentation.from(it))
        } ?: call.respond(HttpStatusCode.NoContent)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.playHandler() {
        val remainingContent = playChallenge(PlayChallenge.ActionData(userId, challengeId))
        call.respond(HttpStatusCode.OK, RemainingContentRepresentation.from(remainingContent))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.registerProgressHandler() {
        val contentId = getFromPath("contentId")
        val episode = progressChallenge(RegisterChallengeProgress.ActionData(userId, challengeId, contentId))
        call.respond(HttpStatusCode.OK, ProgressEpisodeRepresentation.from(EpisodeSummaryRepresentation.from(episode)))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.answerHandler() {
        val contentId = getFromPath("contentId")
        val request = call.receive<AnswerRepresentation>()
        val actionData = AnswerChallengeContent.ActionData(
            playerId = userId,
            challengeId = challengeId,
            contentId = contentId,
            isCorrect = request.isCorrect,
            elapsedTime = request.elapsedTime,
            totalTime = request.totalTime
        )
        answerChallenge(actionData)
        call.respond(HttpStatusCode.OK)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.finishHandler() {
        val finishEpisodeInfo = finishChallenge(FinishChallenge.ActionData(userId, challengeId))
        call.respond(HttpStatusCode.OK, FinishEpisodeRepresentation.from(finishEpisodeInfo))
    }
}
