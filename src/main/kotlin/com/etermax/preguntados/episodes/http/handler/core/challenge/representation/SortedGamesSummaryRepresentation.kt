package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges.*
import kotlinx.serialization.Serializable

@Serializable
data class SortedGamesSummaryRepresentation(
    val games: List<ChallengeSummaryRepresentation>,
    val pendingGames: List<ChallengeSummaryRepresentation>

    companion object {
        fun from(challenges: SortedChallenges) = with(challenges) {
            SortedGamesSummaryRepresentation(
                games = games.map { ChallengeSummaryRepresentation.from(it) },
                pendingGames = pendingGames.map { ChallengeSummaryRepresentation.from(it) }
            )
        }
    }
)