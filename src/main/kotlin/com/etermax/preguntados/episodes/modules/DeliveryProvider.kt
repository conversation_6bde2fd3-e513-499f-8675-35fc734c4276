package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.analytics.http.EpisodeAnalyticsHandler
import com.etermax.preguntados.episodes.core.infrastructure.http.error.ExceptionMapper
import com.etermax.preguntados.episodes.http.HttpApiServer
import com.etermax.preguntados.episodes.http.handler.administrative.AdminEpisodeHandler
import com.etermax.preguntados.episodes.http.handler.administrative.InfoHandler
import com.etermax.preguntados.episodes.http.handler.administrative.StatusHandler
import com.etermax.preguntados.episodes.http.handler.core.*
import com.etermax.preguntados.episodes.http.handler.core.challenge.ChallengeHandler
import com.etermax.preguntados.episodes.http.handler.core.channel.ChannelEpisodesHandler
import com.etermax.preguntados.episodes.http.handler.core.channel.ChannelHandler
import com.etermax.preguntados.episodes.http.handler.core.channel.ProfileHandler
import com.etermax.preguntados.episodes.http.handler.core.channel.admin.AdminChannelHandler
import com.etermax.preguntados.episodes.http.plugins.eteragent.EterAgentPluginFactory
import com.etermax.preguntados.episodes.modules.ActionsProvider.addEpisodesToChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.adminDeleteChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.adminGetChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.answerChallenge
import com.etermax.preguntados.episodes.modules.ActionsProvider.answerContent
import com.etermax.preguntados.episodes.modules.ActionsProvider.challengePlayers
import com.etermax.preguntados.episodes.modules.ActionsProvider.copyEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.createChallenge
import com.etermax.preguntados.episodes.modules.ActionsProvider.createChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.createEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.deleteChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.deleteEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.findAllEpisodes
import com.etermax.preguntados.episodes.modules.ActionsProvider.findChallenge
import com.etermax.preguntados.episodes.modules.ActionsProvider.findChannelById
import com.etermax.preguntados.episodes.modules.ActionsProvider.findEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.findEpisodeDetails
import com.etermax.preguntados.episodes.modules.ActionsProvider.findEpisodesByIds
import com.etermax.preguntados.episodes.modules.ActionsProvider.findFriendsPlayedEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.findReducedChannelById
import com.etermax.preguntados.episodes.modules.ActionsProvider.findSortedChallenges
import com.etermax.preguntados.episodes.modules.ActionsProvider.finishChallenge
import com.etermax.preguntados.episodes.modules.ActionsProvider.finishEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.getEpisodeAnalytics
import com.etermax.preguntados.episodes.modules.ActionsProvider.getEpisodeReports
import com.etermax.preguntados.episodes.modules.ActionsProvider.getFeed
import com.etermax.preguntados.episodes.modules.ActionsProvider.getMyReports
import com.etermax.preguntados.episodes.modules.ActionsProvider.getProfileSummary
import com.etermax.preguntados.episodes.modules.ActionsProvider.overviewChannelsSearch
import com.etermax.preguntados.episodes.modules.ActionsProvider.overviewInfoSearch
import com.etermax.preguntados.episodes.modules.ActionsProvider.playChallenge
import com.etermax.preguntados.episodes.modules.ActionsProvider.playEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.progressChallenge
import com.etermax.preguntados.episodes.modules.ActionsProvider.rateEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.recommendEpisodes
import com.etermax.preguntados.episodes.modules.ActionsProvider.registerContentProgress
import com.etermax.preguntados.episodes.modules.ActionsProvider.registerQuality
import com.etermax.preguntados.episodes.modules.ActionsProvider.removeEpisodesFromChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.reportEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.searchChannels
import com.etermax.preguntados.episodes.modules.ActionsProvider.searchChannelsByLanguage
import com.etermax.preguntados.episodes.modules.ActionsProvider.searchEpisodes
import com.etermax.preguntados.episodes.modules.ActionsProvider.searchPlayersAccount
import com.etermax.preguntados.episodes.modules.ActionsProvider.updateChannel
import com.etermax.preguntados.episodes.modules.ActionsProvider.updateChannelOrderType
import com.etermax.preguntados.episodes.modules.ActionsProvider.updateEpisode
import com.etermax.preguntados.episodes.modules.ActionsProvider.updateEpisodesOrderInChannel
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.eterAgentRepository
import com.etermax.preguntados.episodes.modules.ServicesProvider.clock
import com.etermax.preguntados.reports.http.ReportsHandler

object DeliveryProvider {

    private val exceptionMapper by lazy {
        ExceptionMapper()
    }
    private val infoHandler by lazy {
        InfoHandler(config.app)
    }

    private val statusHandler by lazy {
        StatusHandler()
    }

    private val episodeHandler by lazy {
        EpisodeHandler(createEpisode, deleteEpisode, findEpisode, findEpisodesByIds, copyEpisode, updateEpisode)
    }

    private val adminEpisodeHandler by lazy {
        AdminEpisodeHandler(findAllEpisodes)
    }

    private val reportsHandler by lazy {
        ReportsHandler(reportEpisode, getEpisodeReports, getMyReports)
    }

    private val gamePlayHandler by lazy {
        GamePlayHandler(playEpisode, registerContentProgress, answerContent, finishEpisode, findFriendsPlayedEpisode)
    }

    private val rateEpisodeHandler by lazy {
        RateEpisodeHandler(rateEpisode)
    }

    private val searchEpisodesHandler by lazy {
        SearchEpisodesHandler(searchEpisodes)
    }

    private val getFeedHandler by lazy {
        GetFeedHandler(getFeed)
    }

    private val episodeDetailsHandler by lazy {
        EpisodeDetailsHandler(findEpisodeDetails)
    }

    private val channelHandler by lazy {
        ChannelHandler(
            createChannel,
            searchChannels,
            searchChannelsByLanguage,
            findChannelById,
            findReducedChannelById,
            updateChannel,
            deleteChannel,
            updateChannelOrderType
        )
    }

    private val channelEpisodesHandler by lazy {
        ChannelEpisodesHandler(addEpisodesToChannel, removeEpisodesFromChannel, updateEpisodesOrderInChannel)
    }

    private val profileHandler by lazy {
        ProfileHandler(getProfileSummary)
    }

    private val episodeRecommendationHandler by lazy {
        EpisodeRecommendationHandler(recommendEpisodes)
    }

    private val overviewHandler by lazy {
        OverviewHandler(overviewInfoSearch, overviewChannelsSearch)
    }

    private val searchPlayersAccountHandler by lazy {
        SearchPlayersAccountHandler(searchPlayersAccount)
    }

    private val episodeAnalyticsHandler by lazy {
        EpisodeAnalyticsHandler(getEpisodeAnalytics, clock)
    }

    private val eterAgentPluginFactory by lazy {
        EterAgentPluginFactory(eterAgentRepository)
    }

    private val adminChannelHandler by lazy {
        AdminChannelHandler(adminDeleteChannel, adminGetChannel)
    }

    private val challengeHandler by lazy {
        ChallengeHandler(
            createChallenge,
            findChallenge,
            findSortedChallenges,
            challengePlayers,
            playChallenge,
            answerChallenge,
            progressChallenge,
            finishChallenge
        )
    }

    val registerQualityHandler by lazy {
        RegisterQualityHandler(registerQuality)
    }

    val apiServer by lazy {
        HttpApiServer(
            config.app,
            exceptionMapper,
            eterAgentPluginFactory,
            infoHandler,
            statusHandler,
            episodeHandler,
            gamePlayHandler,
            rateEpisodeHandler,
            searchEpisodesHandler,
            getFeedHandler,
            episodeDetailsHandler,
            channelHandler,
            adminEpisodeHandler,
            reportsHandler,
            channelHandler,
            channelEpisodesHandler,
            profileHandler,
            episodeRecommendationHandler,
            adminChannelHandler,
            overviewHandler,
            searchPlayersAccountHandler,
            episodeAnalyticsHandler,
            challengeHandler,
            registerQualityHandler
        )
    }
}
