package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.analytics.repository.DynamoDBCreatorUserAnalyticsRepository
import com.etermax.preguntados.analytics.repository.DynamoDBEpisodeCountersAnalyticsRepository
import com.etermax.preguntados.analytics.repository.RedisHistogramAnalyticsRepository
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.profile.repository.RedisPlayerFriendsRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.repository.RedisProfileRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.DynamoDBChallengePlayerRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.DynamoDBChallengeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.*
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.*
import com.etermax.preguntados.episodes.core.infrastructure.repository.eteragent.DynamoDBEterAgentRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.notification.DynamoDBEventGroupsRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.DynamoDBProgressRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.rate.DynamoDBRateRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.Tokenizer
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.RedisFilterDataRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.repository.RedisPlayedEpisodeSearchRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.repository.RedisPlayerRecentSearchRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.repository.RedisRecentEpisodeRepository
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.assignChannelIdToEpisodeItem
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.byOwnerAndLastModificationIndex
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.byOwnerIndex
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.challengePlayersByPlayerIndex
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.challengePlayersTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.challengesTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.channelQualityTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.channelScoreTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.channelsEpisodesOrderTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.channelsEpisodesTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.channelsTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.contentMetricsTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.coreReportTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.creatorMetricsTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.dynamoDbEnhancedClient
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.episodeScoreTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.episodeTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.eterAgentTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.eventGroupsTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.progressTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.rateTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.removeChannelIdFromChannelTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.updateChannelEpisodesCountTable
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.updateChannelOrderTypeTable
import com.etermax.preguntados.episodes.modules.RedisProvider.redis
import com.etermax.preguntados.episodes.modules.ServicesProvider.clock
import com.etermax.preguntados.reports.repository.DynamoDBReportRepository
import org.slf4j.LoggerFactory
import java.time.Duration

object RepositoriesProvider {

    private val logger = LoggerFactory.getLogger(this::class.java)

    val episodesRepository by lazy {
        DynamoDBEpisodeRepository(dynamoDbEnhancedClient, episodeTable, byOwnerIndex)
    }

    val progressContentRepository by lazy {
        DynamoDBProgressRepository(dynamoDbEnhancedClient, progressTable)
    }

    val rateRepository by lazy {
        DynamoDBRateRepository(dynamoDbEnhancedClient, rateTable)
    }

    val reportRepository by lazy {
        DynamoDBReportRepository(dynamoDbEnhancedClient, coreReportTable)
    }

    val profileRepository by lazy {
        RedisProfileRepository(redis, config.persistence.mediumTtl)
    }

    val playerFriendsRepository by lazy {
        RedisPlayerFriendsRepository(redis, Duration.ofMinutes(30))
    }

    val updatedEpisodeRepository by lazy {
        RedisNewEpisodeRepository(redis, Duration.ofSeconds(10))
    }

    val pendingEpisodeRepository by lazy {
        RedisPendingEpisodeRepository(redis, config.persistence.veryShortTtl)
    }

    val playerRecentSearchRepository by lazy {
        RedisPlayerRecentSearchRepository(redis, config.persistence.shortTtl)
    }

    val playedEpisodeSearchRepository by lazy {
        RedisPlayedEpisodeSearchRepository(redis, config.persistence.mediumTtl)
    }

    val recentEpisodeRepository by lazy {
        RedisRecentEpisodeRepository(redis, Duration.ofMinutes(60))
    }

    val eterAgentRepository by lazy {
        DynamoDBEterAgentRepository(dynamoDbEnhancedClient, eterAgentTable)
    }

    val eventGroupRepository by lazy {
        DynamoDBEventGroupsRepository(dynamoDbEnhancedClient, eventGroupsTable)
    }

    val tokenizer by lazy {
        Tokenizer()
    }

    val channelRepository by lazy {
        DynamoDBChannelRepository(dynamoDbEnhancedClient, channelsTable)
    }

    val channelByLanguageRepository by lazy {
        DynamoDbChannelByLanguageRepository(dynamoDbEnhancedClient, channelsTable, byOwnerAndLastModificationIndex)
    }

    val channelEpisodesRepository by lazy {
        DynamoChannelEpisodesRepository(
            dynamoDbEnhancedClient,
            updateChannelEpisodesCountTable,
            channelsEpisodesTable
        )
    }

    val channelEpisodesOrderRepository by lazy {
        DynamoChannelEpisodesOrderRepository(
            dynamoDbEnhancedClient,
            channelsEpisodesOrderTable
        )
    }

    val addEpisodesToChannelRepository by lazy {
        DynamoDbAddEpisodesToChannelRepository(
            dynamoDbEnhancedClient,
            assignChannelIdToEpisodeItem,
            channelsTable,
            channelsEpisodesTable
        )
    }

    val filterDataRepository by lazy {
        RedisFilterDataRepository(redis = redis, ttl = Duration.ofMinutes(5))
    }

    val channelDeleteRepository by lazy {
        DynamoDbChannelDeleteRepository(
            dynamoDbEnhancedClient,
            channelsTable,
            channelsEpisodesTable,
            removeChannelIdFromChannelTable,
            channelEpisodesRepository
        )
    }

    val channelUnpublishedEpisodesRepository by lazy {
        RedisChannelUnpublishedEpisodesRepository(
            redis,
            scoreCalculator = { clock.now().toMillis().toDouble() },
            ttl = Duration.ofMinutes(30)
        )
    }

    val channelEpisodeDeleteRepository by lazy {
        DynamoDbChannelEpisodeDeleteRepository(
            dynamoDbEnhancedClient,
            episodeTable,
            updateChannelEpisodesCountTable,
            channelsEpisodesTable,
            episodesRepository
        )
    }

    val channelRemoveEpisodeRepository by lazy {
        DynamoDbChannelRemoveEpisodeRepository(
            dynamoDbEnhancedClient,
            removeChannelIdFromChannelTable,
            updateChannelEpisodesCountTable,
            channelsEpisodesTable
        )
    }

    val channelScoreRepository by lazy {
        DynamoDbChannelScoreRepository(channelScoreTable, channelQualityTable, dynamoDbEnhancedClient)
    }

    val episodeScoreRepository by lazy {
        DynamoDbEpisodeQualityRepository(episodeScoreTable, dynamoDbEnhancedClient)
    }

    val contentAnalyticsRepository by lazy {
        DynamoDBEpisodeCountersAnalyticsRepository(dynamoDbEnhancedClient, contentMetricsTable, clock)
    }

    val creatorAnalyticsRepository by lazy {
        DynamoDBCreatorUserAnalyticsRepository(dynamoDbEnhancedClient, creatorMetricsTable, clock)
    }

    val histogramsRepository by lazy {
        RedisHistogramAnalyticsRepository(redis, clock)
    }

    val challengesRepository by lazy {
        DynamoDBChallengeRepository(dynamoDbEnhancedClient, challengesTable)
    }

    val challengePlayerRepository by lazy {
        DynamoDBChallengePlayerRepository(dynamoDbEnhancedClient, challengePlayersTable, challengePlayersByPlayerIndex)
    }

    val episodePlayersByOwnerRepository by lazy {
        RedisEpisodePlayersByOwnerRepository(
            redis,
            clock,
            Duration.parse(config.playersByOwnerExpirationTime),
            config.isPlayersByOwnerRepositoryEnabled
        )
    }

    val channelUpdateOrderTypeRepository by lazy {
        DynamoDbChannelUpdateOrderTypeRepository(updateChannelOrderTypeTable)
    }

    fun closeRepositories() {
        logger.info("Closing repositories")
        DynamoDBProvider.shutdownClient()
        OpenSearchProvider.shutdownClient()
        RedisProvider.shutdownClient()
        logger.info("Repositories closed")
    }
}
