package com.etermax.preguntados.reports.http

import com.etermax.preguntados.episodes.http.episodeId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeReportsRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.ReportEpisodeRepresentation
import com.etermax.preguntados.episodes.http.userId
import com.etermax.preguntados.reports.action.GetEpisodeReports
import com.etermax.preguntados.reports.action.GetMyReports
import com.etermax.preguntados.reports.action.ReportEpisode
import com.etermax.preguntados.reports.http.representation.MyReportsRepresentation
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import io.ktor.util.pipeline.PipelineContext

class ReportsHandler(
    private val reportEpisode: ReportEpisode,
    private val getEpisodeReports: GetEpisodeReports,
    private val getMyReports: GetMyReports
) : Handler {
    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/{episodeId}") {
                route("/report") { // DEPRECATED
                    post { createReportHandler() }
                }
                route("/reports") {
                    post { createReportHandler() }
                    get { getEpisodeReportsHandler() }
                }
            }
            route("/api/users/{userId}/reports") {
                get { getMyReportsHandler() }
            }
            route("/api/admins/{admin_id}/episodes/{episodeId}/reports") {
                get { adminGetEpisodeReportsHandler() }
                post { adminUpdateReportHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.createReportHandler() {
        val json = call.receive<ReportEpisodeRepresentation>()
        val action = ReportEpisode.ActionData(userId, episodeId, json.reason(), json.comment())
        reportEpisode(action)
        call.respond(HttpStatusCode.Companion.OK)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getEpisodeReportsHandler() {
        val reportsData = getEpisodeReports(GetEpisodeReports.ActionData(userId, episodeId))
        val representation = EpisodeReportsRepresentation.Companion.from(reportsData)
        call.respond(HttpStatusCode.Companion.OK, representation)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getMyReportsHandler() {
        val reports = getMyReports(GetMyReports.ActionData(userId))
        val representation = MyReportsRepresentation.from(reports)
        call.respond(HttpStatusCode.OK, representation)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.adminGetEpisodeReportsHandler() {
        call.respond(HttpStatusCode.Companion.OK)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.adminUpdateReportHandler() {
        call.respond(HttpStatusCode.Companion.OK)
    }
}
