package com.etermax.preguntados.episodes.core.action.channel.episodes

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesOrderRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.order.ChannelSearchOrder
import com.etermax.preguntados.episodes.core.domain.exception.ChannelEpisodesNewOrderCannotBeEmptyException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateEpisodesOrderInChannelShould {
    private lateinit var action: UpdateEpisodesOrderInChannel
    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var channelEpisodesOrderRepository: ChannelEpisodesOrderRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var orderItemCalculator: OrderItemCalculator

    private lateinit var newEpisodesOrder: Map<Int, String>
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = mockk()
        channelEpisodesOrderRepository = mockk(relaxUnitFun = true)
        orderItemCalculator = mockk()
        channelRepository = mockk()

        action = UpdateEpisodesOrderInChannel(
            channelEpisodesRepository,
            channelEpisodesOrderRepository,
            channelRepository,
            orderItemCalculator
        )

        givenChannel()
        every { orderItemCalculator.calculate() } returns MOST_RECENT_ORDER

        newEpisodesOrder = mapOf()
        error = null
    }

    @Test
    fun `fail when channel not found`() = runTest {
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        givenNoChannel()
        whenUpdateOrder()
        thenThrowsException<ChannelNotFoundException>()
    }

    @Test
    fun `fail when channel not owned by current player`() = runTest {
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        givenChannel(ownerId = 200L)
        whenUpdateOrder()
        thenThrowsException<PlayerNotOwnChannelException>()
    }

    @Test
    fun `fail when new orders is empty`() = runTest {
        newEpisodesOrder = emptyMap()
        whenUpdateOrder()
        thenThrowsException<ChannelEpisodesNewOrderCannotBeEmptyException>()
    }

    @Test
    fun `episode changed to position 0`() = runTest {
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 2,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5)

        whenUpdateOrder()

        verify(exactly = 1) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(
                channelId = ChannelMother.ID,
                episodeId = EPISODE_1.episodeId,
                _episodeOrder = MOST_RECENT_ORDER
            )
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `episode changed to position 1`() = runTest {
        newEpisodesOrder = mapOf(1 to EPISODE_2.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 3,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 5500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `episode changed to position 2 for episode contained`() = runTest {
        newEpisodesOrder = mapOf(2 to EPISODE_6.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 4,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 3500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `one episode changed to position 1 and other to position 3`() = runTest {
        newEpisodesOrder = mapOf(
            1 to EPISODE_2.episodeId,
            3 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 5,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 4500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 3500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed to adjacent positions 1 and 2`() = runTest {
        newEpisodesOrder = mapOf(
            1 to EPISODE_2.episodeId,
            2 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 4,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 4667),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 4334)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed to adjacent positions 1 and 2 and another to 4`() = runTest {
        newEpisodesOrder = mapOf(
            1 to EPISODE_2.episodeId,
            2 to EPISODE_6.episodeId,
            4 to EPISODE_5.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 3667),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 3334),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_5.episodeId, _episodeOrder = 2000)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed to last position`() = runTest {
        newEpisodesOrder = mapOf(
            5 to EPISODE_2.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 7,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed from last to last -1 position`() = runTest {
        newEpisodesOrder = mapOf(
            4 to EPISODE_1.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_1.episodeId, _episodeOrder = 2500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed from last to last -2 position`() = runTest {
        newEpisodesOrder = mapOf(
            3 to EPISODE_1.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 5,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_1.episodeId, _episodeOrder = 3500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed from first to last position`() = runTest {
        newEpisodesOrder = mapOf(
            5 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 7,
                ChannelSearchOrder.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    private fun givenChannel(ownerId: Long = PLAYER_ID) {
        coEvery { channelRepository.findById(ChannelMother.ID) } returns ChannelMother.aChannel(ownerId = ownerId)
    }

    private fun givenNoChannel() {
        coEvery { channelRepository.findById(any<String>()) } returns null
    }

    private suspend fun whenUpdateOrder() {
        error = runCatching {
            val data = UpdateEpisodesOrderInChannel.ActionData(PLAYER_ID, ChannelMother.ID, newEpisodesOrder)
            action.invoke(data)
        }.exceptionOrNull()
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        Assertions.assertThat(error)
            .isNotNull
            .isInstanceOf(T::class.java)
    }

    private companion object {
        const val PLAYER_ID = 100L
        const val MOST_RECENT_ORDER = 10000L
        val EPISODE_1 = ChannelMother.aChannelEpisode(episodeId = "episode_1", episodeOrder = 1000)
        val EPISODE_2 = ChannelMother.aChannelEpisode(episodeId = "episode_2", episodeOrder = 2000)
        val EPISODE_3 = ChannelMother.aChannelEpisode(episodeId = "episode_3", episodeOrder = 3000)
        val EPISODE_4 = ChannelMother.aChannelEpisode(episodeId = "episode_4", episodeOrder = 4000)
        val EPISODE_5 = ChannelMother.aChannelEpisode(episodeId = "episode_5", episodeOrder = 5000)
        val EPISODE_6 = ChannelMother.aChannelEpisode(episodeId = "episode_6", episodeOrder = 6000)
    }
}
