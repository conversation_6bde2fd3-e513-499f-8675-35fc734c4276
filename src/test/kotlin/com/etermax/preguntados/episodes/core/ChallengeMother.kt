package com.etermax.preguntados.episodes.core

import com.etermax.preguntados.episodes.core.domain.challenge.Challenge
import com.etermax.preguntados.episodes.core.domain.challenge.EpisodeReference
import java.time.OffsetDateTime
import java.time.OffsetDateTime.now

object ChallengeMother {

    const val ID = "challenge_id"
    const val NAME = "challenge_name"
    const val COVER = "challenge_cover"
    const val OWNER_ID = 123L
    const val EPISODE_ID = "episode_id"
    val CONTENTS = listOf("10", "20", "30")

    fun aChallenge(
        id: String = ID,
        name: String = NAME,
        cover: String = COVER,
        createDate: OffsetDateTime = now(),
        startDate: OffsetDateTime = now(),
        endDate: OffsetDateTime = now().plusDays(1),
        ownerId: Long = OWNER_ID,
        episodeId: String = EPISODE_ID,
        contents: List<String> = CONTENTS
    ) = Challenge(
        id = id,
        createDate = createDate,
        startDate = startDate,
        endDate = endDate,
        expireDate = endDate.plusDays(1),
        ownerId = ownerId,
        reference = EpisodeReference(episodeId, name, cover, contents)
    )
}
