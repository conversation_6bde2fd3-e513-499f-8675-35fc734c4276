package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items

import com.etermax.preguntados.episodes.core.ChannelMother
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ChannelEpisodeItemShould {
    @Test
    fun `not complete episode order with zeros if is 19 characters`() {
        val domain = ChannelMother.aChannelEpisode(
            episodeId = "episode_id",
            episodeOrder = 1234567890123456789
        )

        val item = ChannelEpisodeItem.from(domain)
        assertThat(item.episodeOrder).isEqualTo("1234567890123456789")
    }

    @Test
    fun `complete episode order with two zeros when is 17 characters`() {
        val domain = ChannelMother.aChannelEpisode(
            episodeId = "episode_id",
            episodeOrder = 12345678901234567
        )

        val item = ChannelEpisodeItem.from(domain)
        assertThat(item.episodeOrder).isEqualTo("0012345678901234567")
    }

    @Test
    fun `complete episode order with ten zeros when is 19 characters`() {
        val domain = ChannelMother.aChannelEpisode(
            episodeId = "episode_id",
            episodeOrder = 1234567890
        )

        val item = ChannelEpisodeItem.from(domain)
        assertThat(item.episodeOrder).isEqualTo("0000000001234567890")
    }
}
